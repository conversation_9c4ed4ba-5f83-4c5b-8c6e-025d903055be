import { translator } from '@/i18n/languageLoader';

export const historyKeys = {
  order: 'Order',
  priceSet: 'PriceSet',
} as const;

export const getTranslatedProperties = (): Partial<Record<string, string>> => ({
  assignedDriverId: translator('historyGrid.properties.assignedDriverId'),
  assignedVehicleId: translator('historyGrid.properties.assignedVehicleId'),
  codAmount: translator('historyGrid.properties.codAmount'),
  codCollected: translator('historyGrid.properties.codCollected'),
  isCod: translator('historyGrid.properties.codCollected'),
  description: translator('historyGrid.properties.description'),
  assignedDriverName: translator('historyGrid.properties.assignedDriverName'),
  status: translator('historyGrid.properties.status'),
  comments: translator('historyGrid.properties.comments'),
  deliverySignatureRequired: translator('historyGrid.properties.deliverySignatureRequired'),
  collectionSignatureRequired: translator('historyGrid.properties.collectionSignatureRequired'),
  scheduledCollectionTime: translator('historyGrid.properties.scheduledCollectionTime'),
  scheduledDeliveryTime: translator('historyGrid.properties.scheduledDeliveryTime'),
  actualCollectionTime: translator('historyGrid.properties.actualCollectionTime'),
  actualDeliveryTime: translator('historyGrid.properties.actualDeliveryTime'),
  collectionAddressId: translator('historyGrid.properties.collectionAddressId'),
  deliveryAddressId: translator('historyGrid.properties.deliveryAddressId'),
  declaredValue: translator('historyGrid.properties.declaredValue'),
  internalNotes: translator('historyGrid.properties.internalNotes'),
});
