import { FilterParams, PaginationParams } from '@/api/core/types';
import { translator } from '@/i18n/languageLoader';
import { OrderStatusEnums } from '@/types/enums/orderStatus';

export const pageSizeDropdown = [
  { label: 20, value: 20 },
  { label: 50, value: 50 },
  { label: 100, value: 100 },
];

export const defaultPagination: PaginationParams & FilterParams = {
  pageNumber: 1,
  pageSize: 100,
};

export const orderStatusOptions = [
  {
    label: translator('ordersPage.statusValues.draft'),
    value: OrderStatusEnums.DRAFT,
  },
  {
    label: translator('ordersPage.submitted'),
    value: OrderStatusEnums.SUBMITTED,
  },
  {
    label: translator('ordersPage.statusValues.pending'),
    value: OrderStatusEnums.PENDING,
  },
  {
    label: translator('ordersPage.statusValues.inTransit'),
    value: OrderStatusEnums.IN_TRANSIT,
  },
  {
    label: translator('ordersPage.delivered'),
    value: OrderStatusEnums.COMPLETED,
  },
  {
    label: translator('ordersPage.statusValues.cancelled'),
    value: OrderStatusEnums.CANCELLED,
  },
  {
    label: translator('ordersPage.statusValues.assigned'),
    value: OrderStatusEnums.ASSIGNED,
  },
];

export const imageMimeTypes = [
  'image/jpeg',
  'image/png',
  'image/gif',
  'image/bmp',
  'image/webp',
  'image/tiff',
  'image/x-icon',
  'image/svg+xml',
];

export const excelMimeType = [
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
];

export const textMimeType = ['text/csv'];
