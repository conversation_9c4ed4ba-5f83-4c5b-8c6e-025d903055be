import CustomAgGrid from '@/components/common/agGrid/AgGrid';
import CustomModal from '@/components/common/modal/CustomModal';
import { GridIdConstant } from '@/constant/GridIdConstant';
import { useLanguage } from '@/hooks/useLanguage';
import { IColDef } from '@/types/AgGridTypes';
import { ICellRendererParams } from 'ag-grid-community';
import { Button, Form, Image, Select } from 'antd';
import { useCallback, useMemo, useState } from 'react';
import { DeleteIcon } from '@/assets';
import Icon from '@ant-design/icons';
import { ImageFormateIcon } from '@/assets/icons/imageFormateIcon';
import { IPackageModelState } from '../orderPackages/orderPackages.types';
import { UploadIcon } from '@/assets/icons/uploadIcon';
import { IAttachments, IImagePreviewState } from './orderAttachments.types';
import { PdfIcon } from '@/assets/icons/pdfIcon';
import { DownloadIcon } from '@/assets/icons/downloadIcon';
import CustomUpload from '@/components/common/customUpload/CustomUpload';
import { useWatch } from 'antd/es/form/Form';
import { customAlert } from '@/components/common/customAlert/CustomAlert';
import { fileUploadService, fileUploadServiceHook } from '@/api/fileUpload/useFileUploads';
import { useParams } from 'react-router-dom';
import { GetUploadedFileDto } from '@/api/fileUpload/fileUpload.types';
import { dateFormatter } from '@/lib/helper/dateHelper';
import { useNotificationManager } from '@/hooks/useNotificationManger';
import useThrottle from '@/hooks/useThrottle';
import { excelMimeType, imageMimeTypes, textMimeType } from '@/constant/generalConstant';
import { RuleObject } from 'antd/es/form';
import { formSizeValidator } from '@/lib/FormValidators';
import { FILE_UPLOAD_CONSTANTS } from '@/constant/fileUploadsConstants';

const OrdersAttachmentsComponent = () => {
  const { id } = useParams();

  const { data: attachments, refetch: refetchAttachments } = fileUploadServiceHook.useEntity<
    GetUploadedFileDto[]
  >(`entity/order/${id}`);

  const notificationManager = useNotificationManager();

  const addAttachmentsMutation = fileUploadServiceHook.useCreateByCustomUrl('upload');

  const deleteAttachmentsMutation = fileUploadServiceHook.useDelete({
    onSuccess: async () => {
      notificationManager.success({
        message: t('common.success'),
        description: t('ordersPage.attachmentsTab.attachmentDeletedSuccessfully'),
      });
      await refetchAttachments();
    },
  });

  const [isModelOpen, setIsModelOpen] = useState<IPackageModelState>({
    isOpen: false,
    isEdit: false,
    packageId: undefined,
  });
  const { t } = useLanguage();

  const uploadTypeOptions = useMemo(() => {
    return [
      {
        value: FILE_UPLOAD_CONSTANTS.COLLECTION_SIGNATURE,
        label: t('ordersPage.attachmentsTab.uploadTypeOptions.collectionSignature'),
      },
      {
        value: FILE_UPLOAD_CONSTANTS.DELIVERY_SIGNATURE,
        label: t('ordersPage.attachmentsTab.uploadTypeOptions.deliverySignature'),
      },
      {
        value: FILE_UPLOAD_CONSTANTS.FILE_ATTACHMENTS,
        label: t('ordersPage.attachmentsTab.uploadTypeOptions.fileAttachment'),
      },
    ];
  }, [t]);

  const [packagesForm] = Form.useForm();
  const [visible, setVisible] = useState<IImagePreviewState>({
    isPreviewVisible: false,
    src: undefined,
  });

  const uploadTypeWatcher = useWatch('type', packagesForm);

  const onFinish = useThrottle(async (values: any) => {
    try {
      const addAttachmentPayload = {
        file: values.file.file,
        entityType: 'order',
        entityId: id as string,
        type: values.type,
      };

      await addAttachmentsMutation.mutateAsync(addAttachmentPayload);

      notificationManager.success({
        message: t('common.success'),
        description: t('ordersPage.attachmentsTab.attachmentAddedSuccessfully'),
      });
      await refetchAttachments();

      setIsModelOpen({ isOpen: false, isEdit: false, packageId: undefined });
    } catch (error) {
      notificationManager.error({
        message: t('common.error'),
        description: t('ordersPage.attachmentsTab.failedToAddAttachment'),
      });
    }
  }, 3000);

  const handleDownload = useThrottle(async (attachment: IAttachments | undefined) => {
    try {
      if (attachment && attachment.id) {
        const response = await fileUploadService.getById<any>(
          `download/${attachment.id}`,
          undefined,
          {
            responseType: 'blob',
          }
        );
        const url = window.URL.createObjectURL(response);

        const a = document.createElement('a');
        a.href = url;
        a.download = attachment.originalFilename;
        document.body.appendChild(a);
        a.click();
        a.remove();
        window.URL.revokeObjectURL(url);
      }
    } catch (err) {
      notificationManager.error({
        message: t('common.error'),
        description: t('ordersPage.attachmentsTab.failedToDownloadItem'),
      });
    }
  }, 3000);

  const deleteAttachmentConfirmation = useCallback(
    (cellData: IAttachments) => {
      if (cellData?.id) {
        customAlert.error({
          title: `${t('zonePage.confirmDeleteZone')}`.replace(
            'this',
            `"${cellData?.originalFilename}"`
          ),
          message: t('ordersPage.attachmentsTab.deleteConfirmationMessage'),
          secondButtonFunction: () => customAlert.destroy(),
          firstButtonFunction: async () => {
            await deleteAttachmentsMutation.mutateAsync(cellData.id);
            customAlert.destroy();
          },
          firstButtonTitle: `${t('common.delete')}`,
          secondButtonTitle: `${t('common.cancel')}`,
        });
      }
    },
    [deleteAttachmentsMutation, t]
  );

  const showAttachmentHandler = (params: ICellRendererParams) => {
    imageMimeTypes.includes(params?.data?.mimeType) &&
      setVisible({ isPreviewVisible: true, src: params.data.url });
    // preview in new tab for only pdf
    params?.data?.mimeType === 'application/pdf' && window.open(params.data.url, '_blank');
  };

  const attachmentCellRenderer = useCallback((params: ICellRendererParams) => {
    const isNotReviewable =
      excelMimeType.includes(params.data.mimeType) || textMimeType.includes(params.data.mimeType);

    return (
      <div
        className={`flex items-center gap-2 ${!isNotReviewable ? 'cursor-pointer' : ''}`}
        onClick={() => showAttachmentHandler(params)}
      >
        <Icon
          component={imageMimeTypes.includes(params.data.mimeType) ? ImageFormateIcon : PdfIcon}
        />
        <span className={`${!isNotReviewable ? 'underline' : 'cursor-default'} cell-values`}>
          {params.value}
        </span>
      </div>
    );
  }, []);

  const attachmentTypeCellRenderer = useCallback(
    (params: ICellRendererParams<GetUploadedFileDto>) =>
      params.data?.type === FILE_UPLOAD_CONSTANTS.COLLECTION_SIGNATURE
        ? t('ordersPage.attachmentsTab.uploadTypeOptions.collectionSignature')
        : params.data?.type === FILE_UPLOAD_CONSTANTS.DELIVERY_SIGNATURE
          ? t('ordersPage.attachmentsTab.uploadTypeOptions.deliverySignature')
          : t('ordersPage.attachmentsTab.uploadTypeOptions.fileAttachment'),
    [t]
  );

  const colDefs: IColDef[] = useMemo(
    () => [
      {
        field: `originalFilename`,
        headerName: t('ordersPage.attachmentsTab.attachmentFile'),
        type: 'text',
        visible: true,
        sortable: true,
        unSortIcon: true,
        minWidth: 130,
        flex: 1,
        cellRenderer: attachmentCellRenderer,
      },
      {
        field: `type`,
        headerName: t('ordersPage.attachmentsTab.type'),
        type: 'text',
        visible: true,
        sortable: true,
        unSortIcon: true,
        minWidth: 130,
        flex: 1,
        cellRenderer: attachmentTypeCellRenderer,
      },
      {
        field: `createdAt`,
        headerName: t('ordersPage.attachmentsTab.dateAdded'),
        type: 'date',
        visible: true,
        sortable: true,
        unSortIcon: true,
        minWidth: 130,
        flex: 1,
        cellRenderer: (params: ICellRendererParams) => {
          const value = params.value ? dateFormatter(params.value) : '';
          return value;
        },
      },
      {
        headerName: t('vehiclePage.colDefs.action'),
        pinned: 'right',
        maxWidth: 70,
        minWidth: 70,
        sortable: false,
        resizable: false,
        visible: true,
        cellRenderer: (params: ICellRendererParams<IAttachments>) => {
          return (
            <div className="flex gap-2 h-full justify-center items-center">
              <Icon
                component={DownloadIcon}
                className="cursor-pointer"
                alt={t('ordersPage.attachmentsTab.downloadTitle')}
                value={'download'}
                title={t('ordersPage.attachmentsTab.downloadTitle')}
                onClick={() => handleDownload(params?.data)}
              />
              <Icon
                component={DeleteIcon}
                className="cursor-pointer"
                alt={t('ordersPage.attachmentsTab.deleteTitle')}
                title={t('ordersPage.attachmentsTab.deleteTitle')}
                value={'delete'}
                onClick={() => deleteAttachmentConfirmation(params.data as IAttachments)}
              />
            </div>
          );
        },
      },
    ],
    [
      attachmentCellRenderer,
      attachmentTypeCellRenderer,
      deleteAttachmentConfirmation,
      handleDownload,
      t,
    ]
  );

  return (
    <div className="h-full">
      <CustomModal
        modalTitle={t('ordersPage.attachmentsTab.uploadAttachment')}
        modalDescription={''}
        open={isModelOpen.isOpen}
        destroyOnClose
        maskClosable={false}
        onCancel={() => setIsModelOpen({ isOpen: false, isEdit: false, packageId: undefined })}
        className="!h-[80%] flex items-center"
        footer={
          <div className="flex gap-2 w-full justify-end">
            <Button
              onClick={() =>
                setIsModelOpen({
                  isOpen: false,
                  isEdit: false,
                  packageId: undefined,
                })
              }
              className="hover:!text-black hover:!border-gray-300"
            >
              {t('common.cancel')}
            </Button>
            {
              <Button
                className="bg-primary-600 hover:!bg-primary-600 text-white hover:!text-white"
                htmlType="submit"
                form="attachmentsForm"
                loading={addAttachmentsMutation.isPending}
                onClick={() => {
                  packagesForm.getFieldError('image');
                }}
              >
                {isModelOpen.isEdit ? t('common.update') : t('common.add')}
              </Button>
            }
          </div>
        }
      >
        <Form
          name="attachmentsForm"
          className="custom-form"
          layout={'vertical'}
          form={packagesForm}
          preserve={false}
          onFinish={onFinish}
        >
          <div className="grid gap-x-6 gap-y-3.5 grid-cols-1 sm:w-[500px]">
            <Form.Item
              label={t('ordersPage.attachmentsTab.uploadType')}
              validateFirst
              name="type"
              validateDebounce={2000}
              rules={[
                {
                  required: true,
                  message: t('ordersPage.attachmentsTab.requiredUploadType'),
                },
                {
                  validator: (_: RuleObject, value) => {
                    const existingType = attachments?.some((item) => item.type === value);

                    if (
                      (value === FILE_UPLOAD_CONSTANTS.COLLECTION_SIGNATURE ||
                        value === FILE_UPLOAD_CONSTANTS.DELIVERY_SIGNATURE) &&
                      existingType
                    ) {
                      return Promise.reject(
                        new Error(t('ordersPage.attachmentsTab.signatureAlreadyExists'))
                      );
                    }
                    return Promise.resolve();
                  },
                  warningOnly: true,
                },
              ]}
            >
              <Select
                options={uploadTypeOptions}
                placeholder={t('ordersPage.attachmentsTab.selectPlaceholder')}
                prefixCls="custom-select"
                onSelect={() => packagesForm.resetFields(['file'])}
                showSearch
                filterOption={(input, option) =>
                  (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                }
              />
            </Form.Item>
            <Form.Item
              name="file"
              rules={[
                {
                  required: true,
                  message: t('ordersPage.attachmentsTab.requiredAttachmentOrFile'),
                },
                {
                  validator: (_: RuleObject, value) => formSizeValidator(value, 5),
                },
              ]}
              validateTrigger="onChange"
            >
              <CustomUpload
                name="file"
                label={t('ordersPage.attachmentsTab.uploadLabel')}
                form={packagesForm}
                uploadComponentProps={{
                  accept:
                    uploadTypeWatcher === 'File attachment'
                      ? '.xlsx,.xls,.csv,.pdf,image/*'
                      : 'image/*',
                }}
              />
            </Form.Item>
          </div>
        </Form>
      </CustomModal>
      <div className="pr-4">
        <Image
          width={200}
          style={{ display: 'none' }}
          src={visible.src}
          preview={{
            visible: visible.isPreviewVisible,
            src: visible.src,
            onVisibleChange: (value) => {
              setVisible({ ...visible, isPreviewVisible: value });
            },
          }}
        />
        <header className="flex justify-end items-center py-4">
          <Button
            className=" h-[40px] border-[1px] rounded-[8px] bg-primary-600 text-white font-[500] hover:!bg-primary-600 hover:!text-white"
            icon={<UploadIcon />}
            onClick={() => {
              setIsModelOpen({ isOpen: true, isEdit: false, packageId: undefined });
            }}
          >
            {t('ordersPage.attachmentsTab.uploadFiles')}
          </Button>
        </header>
        <main>
          <CustomAgGrid
            rowData={attachments}
            columnDefs={colDefs}
            className="!h-[74vh] lg:!h-[75vh]"
            gridId={GridIdConstant.GRID_WRAPPER_FOR_CHILDREN}
            emptyState={{
              title: t('ordersPage.attachmentsTab.noFilesOrAttachmentsFound'),
              description: '',
            }}
          />
        </main>
      </div>
    </div>
  );
};
export default OrdersAttachmentsComponent;
