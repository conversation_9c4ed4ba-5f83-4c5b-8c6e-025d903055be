import { FilterBoxDeleteOutlinedIcon, PlusModifierIcon, SelectSuffixIcon } from '@/assets';
import { Button, Form, InputNumber, Select } from 'antd';
import '../../modifiers.css';
import { FormInstance } from 'antd/lib';
import { IConfigureTierFormData } from '../../priceModifiers.types';
import { RangeFromOperator, RangeToOperator } from '../../priceModifiers.types';
import { useLanguage } from '@/hooks/useLanguage';
import { useEffect } from 'react';
import { IPriceModifier } from '@/api/priceModifier/priceModifier.types';
import { useParams } from 'react-router-dom';
interface IConfigureTiersProps {
  configureTiersForm: FormInstance<IConfigureTierFormData>;
  selectedType: string;
  setIsConfigureTierModal: React.Dispatch<React.SetStateAction<boolean>>;
  setConfigureTiersFormData: React.Dispatch<React.SetStateAction<IConfigureTierFormData>>;
  formData: IPriceModifier;
}
const ConfigureTieredForm: React.FC<IConfigureTiersProps> = (props) => {
  const { t } = useLanguage();
  const { id } = useParams();

  const {
    configureTiersForm,
    selectedType,
    setIsConfigureTierModal,
    setConfigureTiersFormData,
    formData,
  } = props;
  const operatorsForFrom = [
    { label: '>', value: 'GreaterThan' },
    { label: '>=', value: 'GreaterThanOrEqual' },
  ];
  const operatorsForTo = [
    { label: '<', value: 'LessThan' },
    { label: '<=', value: 'LessThanOrEqual' },
  ];
  const isAmount: string = selectedType?.includes('$')
    ? t('priceModifiers.configureTiersForm.amount')
    : t('priceModifiers.configureTiersForm.percentage');
  const handleOnFinish = (e: IConfigureTierFormData) => {
    const updatedTiers = (e.tieredRanges || []).map(tier => ({
      ...tier,
      fromValue: Number(tier.fromValue),
      toValue: Number(tier.toValue),
      value: Number(tier.value),
    }));
    setConfigureTiersFormData({ ...e, tieredRanges: updatedTiers, tieredDefaultValue: Number(e.tieredDefaultValue) });
    setIsConfigureTierModal(false);
  };
  useEffect(() => {
    if (id && formData) {
      if (!formData.tieredRanges || formData.tieredRanges.length === 0) {
        configureTiersForm.setFieldsValue({
          ...formData,
          tieredRanges: [{ fromValue: 0, toValue: undefined, fromOperator: RangeFromOperator.GreaterThan, toOperator: RangeToOperator.LessThan }],
        });
      } else {
        const newTiers = (formData.tieredRanges || []).map(tier => ({
          ...tier,
          fromValue: (tier.fromValue === undefined || tier.fromValue === null || typeof tier.fromValue !== 'number' || isNaN(tier.fromValue)) ? 0 : tier.fromValue,
          toValue: (tier.toValue === undefined || tier.toValue === null || typeof tier.toValue !== 'number' || isNaN(tier.toValue)) ? undefined : tier.toValue,
        }));
        configureTiersForm.setFieldsValue({ ...formData, tieredRanges: newTiers });
      }
    }
  }, [formData, id, configureTiersForm]);
  return (
    <div>
      <Form
        scrollToFirstError
        form={configureTiersForm}
        onFinish={(e) => handleOnFinish(e)}
        initialValues={{ tieredRanges: [{ fromValue: 0, toValue: 'None' }] }}
        autoComplete="off"
        layout="vertical"
        name="configureTiersForm"
      >
        <Form.List name="tieredRanges">
          {(fields, { add, remove }) => (
            <div>
              <Form.Item className="flex justify-end w-[99%] mb-0 h-[50px]">
                <Button
                  className="h-[40px] hover:!border-gray-300 hover:!text-black "
                  icon={<PlusModifierIcon />}
                  onClick={() => add({ fromValue: 0, toValue: 'none' })}
                >
                  {t('priceModifiers.configureTiersForm.add')}
                </Button>
              </Form.Item>
              <div className="w-full flex flex-col max-h-[300px] overflow-y-auto">
                {fields?.map((field, index) => (
                  <div className="w-[99%] flex gap-3 items-center">
                    <div className="flex w-full gap-3">
                      <Form.Item
                        dependencies={['tieredRanges', field.name, 'toValue']}
                        {...field}
                        name={[field.name, 'fromValue']}
                        className="required-tag-item mb-3 w-2/6 p-0"
                        label={t('priceModifiers.configureTiersForm.start')}
                        rules={[
                          { required: true, message: t('priceModifiers.formulaDescription.applicableRangeRequired') },
                          {
                            validator: (_, value) => {
                              const to = configureTiersForm.getFieldValue(['tieredRanges', field.name, 'toValue']);
                              if ((value === 0 || value === '0') && (to === 'none' || to === undefined || to === null || to === '')) {
                                return Promise.reject(new Error(t('priceModifiers.formulaDescription.applicableRangeRequired')));
                              }
                              return Promise.resolve();
                            },
                          },
                        ]}
                      >
                        <InputNumber
                          className="input-number-start"
                          onChange={() => {
                            configureTiersForm.validateFields([["tieredRanges", field.name, "toValue"]]);
                          }}
                          min={0}
                          defaultValue={0}
                          addonBefore={
                            <Form.Item
                              initialValue={'GreaterThan'}
                              className="mb-0"
                              name={[field.name, 'fromOperator']}
                            >
                              <Select
                                className="select-item"
                                suffixIcon={<SelectSuffixIcon />}
                                options={operatorsForFrom}
                              />
                            </Form.Item>
                          }
                        />
                      </Form.Item>
                      <Form.Item
                        dependencies={['tieredRanges', field.name, 'fromValue']}
                        name={[field.name, 'toValue']}
                        rules={[
                          { required: true, message: t('priceModifiers.formulaDescription.applicableRangeRequired') },
                          {
                            validator: (_, value) => {
                              const from = configureTiersForm.getFieldValue(['tieredRanges', field.name, 'fromValue']);
                              if ((value === 'none' || value === undefined || value === null || value === '') && (from === 0 || from === '0')) {
                                return Promise.reject(new Error(t('priceModifiers.formulaDescription.applicableRangeRequired')));
                              }
                              // If TO is a number, it must be >= FROM
                              if (value !== 'none' && value !== undefined && value !== null && value !== '' && from !== undefined && from !== null && from !== '' && Number(value) < Number(from)) {
                                return Promise.reject(new Error(t('priceModifiers.formulaDescription.endValueMustBeGreaterOrEqual')));
                              }
                              return Promise.resolve();
                            },
                          },
                        ]}
                        className="required-tag-item mb-3 w-2/6"
                        label="End"
                      >
                        <InputNumber
                          min={configureTiersForm.getFieldValue(['tieredRanges', field.name, 'fromValue']) || 1}
                          className="input-number-start"
                          value={configureTiersForm.getFieldValue(['tieredRanges', field.name, 'toValue']) === 'none' ? undefined : configureTiersForm.getFieldValue(['tieredRanges', field.name, 'toValue'])}
                          placeholder={'None'}
                          onChange={(value) => {
                            if (value === undefined || value === null || value === '') {
                              configureTiersForm.setFieldValue(['tieredRanges', field.name, 'toValue'], 'none');
                            } else {
                              configureTiersForm.setFieldValue(['tieredRanges', field.name, 'toValue'], value);
                            }
                            configureTiersForm.validateFields([["tieredRanges", field.name, "fromValue"]]);
                          }}
                          addonBefore={
                            <Form.Item
                              initialValue={'LessThan'}
                              className="mb-0"
                              name={[field.name, 'toOperator']}
                            >
                              <Select
                                className="select-item"
                                suffixIcon={<SelectSuffixIcon />}
                                options={operatorsForTo}
                              />
                            </Form.Item>
                          }
                        />
                      </Form.Item>
                      <Form.Item
                        rules={[
                          {
                            required: true,
                            message: t('priceModifiers.configureTiersForm.amountIsRequired', {
                              Amount: isAmount,
                            }),
                          },
                          {
                            validator: (_, value) => {
                              if (selectedType?.includes('$')) {
                                if (value > 999999999) {
                                  return Promise.reject(
                                    new Error(t('priceModifiers.maximumValueExceeded'))
                                  );
                                } else {
                                  return Promise.resolve();
                                }
                              } else {
                                if (value > 999) {
                                  return Promise.reject(
                                    new Error(t('priceModifiers.maximumPercentExceeded'))
                                  );
                                } else {
                                  return Promise.resolve();
                                }
                              }
                            },
                          },
                        ]}
                        name={[field.name, 'value']}
                        className="required-tag-item mb-3 w-2/6"
                        label={isAmount}
                      >
                        <InputNumber className="input-number-start" addonBefore={selectedType} />
                      </Form.Item>
                    </div>
                    {
                      <Form.Item className="mb-0 mt-4">
                        <Button
                          disabled={fields.length === 1 && index === 0}
                          icon={<FilterBoxDeleteOutlinedIcon />}
                          onClick={() => remove(field.name)}
                          className={
                            'text-red-500 pt-2 cursor-pointer hover:!border-gray-300 h-[40px] !w-[40px]'
                          }
                        />
                      </Form.Item>
                    }
                  </div>
                ))}
              </div>
            </div>
          )}
        </Form.List>
        <Form.Item
          rules={[
            {
              validator: (_, value) => {
                if (selectedType?.includes('$')) {
                  if (value > 999999999) {
                    return Promise.reject(new Error(t('priceModifiers.maximumValueExceeded')));
                  } else {
                    return Promise.resolve();
                  }
                } else {
                  if (value > 999) {
                    return Promise.reject(new Error(t('priceModifiers.maximumPercentExceeded')));
                  } else {
                    return Promise.resolve();
                  }
                }
              },
            },
          ]}
          className="w-full mb-0 mt-[25px]"
          label={t('priceModifiers.configureTiersForm.defaultAmountIfNoMatchingTiers', {
            amount: isAmount.toLowerCase(),
          })}
          name={'tieredDefaultValue'}
          initialValue={0}
        >
          <InputNumber className="input-number-start w-full" addonBefore={selectedType} />
        </Form.Item>
      </Form>
    </div>
  );
};
export default ConfigureTieredForm;
