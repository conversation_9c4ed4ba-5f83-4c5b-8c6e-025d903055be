import { OrderStatusEnums } from '@/types/enums/orderStatus';

export type IOrserStatus =
  | 'Draft'
  | 'Submitted'
  | 'Pending'
  | 'Assigned'
  | 'In_Progress'
  | 'Completed'
  | 'Cancelled';
export interface CreateOrderItemDto {
  packageTemplateId?: string;
  itemType: string;
  quantity: number;
  weight?: number;
  weightUnit?: string;
  length?: number;
  width?: number;
  height?: number;
  dimensionUnit?: string;
  declaredValue?: number;
  description?: string;
  notes?: string;
  imageUrl?: string;
  metadata?: {
    [key: string]: any;
  };
}

export interface ResponseOrderItemDto extends CreateOrderItemDto {
  id: string;
  orderId: string;
  createdAt: string;
  updatedAt: string;
  packageTemplateName?: string;
}

export interface IOrderItemPaginatedResponse {
  data: ResponseOrderItemDto[];
  total: number;
  skip: number;
  limit: number;
}

export interface CreateOrdersDto {
  referenceNumber?: string;
  trackingNumber: string;
  status?: string;
  customerId: string;
  requestedById: string;
  submittedById: string;
  packageTemplateId?: string;
  collectionAddressId: string;
  collectionContactName?: string;
  collectionCompanyName?: string;
  collectionAddress?: string;
  collectionAddressLine1?: string;
  collectionAddressLine2?: string;
  collectionCity?: string;
  collectionProvince?: string;
  collectionPostalCode?: string;
  collectionCountry?: string;
  collectionEmail?: string;
  collectionPhone?: string;
  collectionPhoneExtension?: string;
  collectionInstructions?: string;
  collectionSignatureRequired?: boolean;
  assignedVehicleDescription: string;
  scheduledCollectionTime?: string;
  collectionZoneId?: string;
  collectionZoneName?: string;
  deliveryAddressId: string;
  deliveryContactName?: string;
  deliveryCompanyName?: string;
  deliveryAddress?: string;
  deliveryAddressLine1?: string;
  deliveryAddressLine2?: string;
  deliveryCity?: string;
  deliveryProvince?: string;
  deliveryPostalCode?: string;
  deliveryCountry?: string;
  deliveryEmail?: string;
  deliveryPhone?: string;
  deliveryPhoneExtension?: string;
  deliveryInstructions?: string;
  deliverySignatureRequired?: boolean;
  scheduledDeliveryTime?: string;
  deliveryZoneId?: string;
  deliveryZoneName?: string;
  totalItems?: number;
  totalVolume?: number;
  isLocked: boolean;
  totalWeight?: number;
  declaredValue?: number;
  vehicleTypeId?: string;
  priceSet?: string;
  assignedDriverId?: string;
  assignedDriverName?: string;
  assignedVehicleId?: string;
  assignedVehicleName?: string;
  codAmount?: number;
  priceSetId?: string;
  basePriceType?: string;
  distance?: number;
  distanceUnit?: string;
  basePrice: string;
  codCollected?: boolean;
  isCod?: boolean;
  isInsurance: boolean;
  optionsPrice?: number;
  description?: string;
  totalPrice?: number;
  paymentStatus: string;
  billingStatus?: string;
  miscAdjustment?: number;
  comments?: string;
  internalNotes?: string;
  items?: CreateOrderItemDto[];
  serviceLevel?: string;
  customerName?: string;
  companyName?: string;
  customerEmail?: string;
  customerPhoneNumber?: string;
  customerContactName?: string;
  invoiceNumber?: string;
  customFields?: {
    [key: string]: any;
  };
  metadata?: {
    [key: string]: any;
  };
  acceptPriceChanges?: boolean;
}

export interface IResponseOrderDto extends CreateOrdersDto {
  id: string;
  orderId: string;
  createdAt: string;
  updatedAt: string;
}

export interface IOrderPaginatedResponse {
  data: IResponseOrderDto[];
  total: number;
  skip: number;
  limit: number;
}

export interface IUpdateOrderStatusPayload {
  status: OrderStatusEnums;
  reason: string;
  comments: string;
}

export interface IOrderPackages {
  id: string;
  orderId: string;
  name: string;
  description: string;
  quantity: number;
  weight: number;
  dimensions: {
    length: number;
    width: number;
    height: number;
  };
  price: number;
}
