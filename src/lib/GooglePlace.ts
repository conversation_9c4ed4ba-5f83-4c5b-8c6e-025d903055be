const checkAddressString = (name: string, str: string | null) => {
  if (!str) {
    return name;
  }
  const result = str?.substring(0, name.length);
  return name === result ? str : `${name}, ${str}`;
};

export const googlePlaceDataMasking = (data: google.maps.places.PlaceResult) => {
  const { address_components, geometry, name } = data;

  const maskedObj: { [key: string]: any } = {};

  maskedObj.latitude = geometry?.location?.lat() ?? null;
  maskedObj.longitude = geometry?.location?.lng() ?? null;

  address_components?.forEach((component) => {
    const types = component.types || [];

    // City / Locality
    if (
      types.includes('locality') ||
      types.includes('postal_town') ||
      types.includes('sublocality') ||
      types.includes('administrative_area_level_3')
    ) {
      maskedObj.city = component.long_name;
    }

    // State / Province
    if (types.includes('administrative_area_level_1')) {
      maskedObj.state = component.long_name;
    }

    // District / County
    if (types.includes('administrative_area_level_2')) {
      maskedObj.district = component.long_name;
    }

    // Country
    if (types.includes('country')) {
      maskedObj.country = component.long_name;
      maskedObj.countryCode = component.short_name; // ISO alpha-2 country code
    }

    // Postal Code
    if (
      types.includes('postal_code') ||
      types.includes('postal_code_prefix') ||
      types.includes('postal_code_suffix')
    ) {
      maskedObj.postalCode = component.long_name;
    }

    // Route (Street name)
    if (types.includes('route')) {
      maskedObj.street = component.long_name;
    }

    // Street Number
    if (types.includes('street_number')) {
      maskedObj.streetNumber = component.long_name;
    }

    // Premise (like building, suite)
    if (types.includes('premise')) {
      maskedObj.premise = component.long_name;
    }

    // Subpremise (like apartment number)
    if (types.includes('subpremise')) {
      maskedObj.subpremise = component.long_name;
    }

    // Neighborhood
    if (types.includes('neighborhood')) {
      maskedObj.neighborhood = component.long_name;
    }
  });

  if (name) {
    maskedObj.addressLine1 = name;
    maskedObj.formattedAddress = checkAddressString(name, data?.formatted_address || null);
  }

  return maskedObj;
};
